package de.adesso.fischereiregister.registerservice.drools.dmn.adapter;

import de.adesso.fischereiregister.core.exceptions.RulesProcessingException;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.PaymentType;
import de.adesso.fischereiregister.core.ports.TenantInformationPort;
import de.adesso.fischereiregister.core.ports.contracts.LicenseInformation;
import de.adesso.fischereiregister.core.ports.contracts.TaxPriceInformation;
import de.adesso.fischereiregister.registerservice.drools.dmn.DmnDroolsService;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.DmnLicenseType;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.input.LicenseInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.license.configuration.output.LicenseInformationOutput;
import de.adesso.fischereiregister.registerservice.drools.dmn.mapper.DmnLicenseTypeMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.mapper.DmnProcessingTypeMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.mapper.LicenseInformationOutputMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.mapper.TaxInformationResultMapper;
import de.adesso.fischereiregister.registerservice.drools.dmn.model.DmnProcessingType;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationInput;
import de.adesso.fischereiregister.registerservice.drools.dmn.tax.configuration.TaxInformationOutput;
import de.adesso.fischereiregister.registerservice.fishing_license_export.port.TenantRulesValuesPort;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Service
@AllArgsConstructor
public class TenantRulesValuesAdapter implements TenantRulesValuesPort, TenantInformationPort {

    private final DmnDroolsService dmnDroolsService;

    @Override
    public BigDecimal retrieveFeeAnalog(FederalState federalState) throws RulesProcessingException {
        try {
            return BigDecimal.valueOf(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.REGULAR, DmnProcessingType.ANALOG).get(0).getFeeAmount());
        } catch (Exception e) {
            throw new RulesProcessingException("Error determining fees DmnLicenseType.REGULAR, DmnProcessingType.ANALOG for federal state: " + federalState);
        }
    }

    @Override
    public BigDecimal retrieveFeeDigital(FederalState federalState) throws RulesProcessingException {
        try {
            return BigDecimal.valueOf(dmnDroolsService.getLicenseInformation(federalState, DmnLicenseType.REGULAR, DmnProcessingType.DIGITAL).get(0).getFeeAmount());
        } catch (Exception e) {
            throw new RulesProcessingException("Error determining fees DmnLicenseType.REGULAR, DmnProcessingType.DIGITAL for federal state: " + federalState);
        }
    }

    @Override
    public TaxPriceInformation getTaxPriceInformation(
            FederalState federalState,
            int years,
            boolean officeFeeAlreadyPayed,
            PaymentType paymentType) throws RulesProcessingException {

        final TaxInformationInput dmnInput = new TaxInformationInput(
                LocalDate.now(),
                BigDecimal.valueOf(years),
                DmnProcessingTypeMapper.INSTANCE.toDmnProcessingType(paymentType),
                officeFeeAlreadyPayed);

        final List<TaxInformationOutput> results = dmnDroolsService.evaluateTaxRules(dmnInput, federalState);

        if (results.size() != 1) {
            throw new RulesProcessingException("No results found for license information");
        }

        return TaxInformationResultMapper.INSTANCE.toCoreModel(results.getFirst());
    }

    @Override
    public LicenseInformation getLicenseInformation(
            FederalState federalState,
            LicenseType licenseType,
            PaymentType paymentType) throws RulesProcessingException {

        final LicenseInformationInput dmnInput = new LicenseInformationInput(
                DmnLicenseTypeMapper.INSTANCE.toDmnLicenseType(licenseType),
                LocalDate.now(),
                DmnProcessingTypeMapper.INSTANCE.toDmnProcessingType(paymentType));

        List<LicenseInformationOutput> results = dmnDroolsService.evaluateLicenseRules(dmnInput, federalState);

        if (results.size() != 1) {
            throw new RulesProcessingException("No results found for license information");
        }

        return LicenseInformationOutputMapper.INSTANCE.toCoreModel(results.getFirst());
    }
}
