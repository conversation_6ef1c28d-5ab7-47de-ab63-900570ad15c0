package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.protocol.service.model.InspectionsStatisticsResult;
import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.registerservice.domain.mapper.BansStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.CertificationsStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.LicensesStatisticsMapper;
import de.adesso.fischereiregister.registerservice.domain.mapper.TaxesStatisticsMapper;
import de.adesso.fischereiregister.registerservice.statistics.bans_statistics.BansStatistics;
import de.adesso.fischereiregister.registerservice.statistics.bans_statistics.BansStatisticsTransformationService;
import de.adesso.fischereiregister.registerservice.statistics.certifications_statistics.CertificationsStatistics;
import de.adesso.fischereiregister.registerservice.statistics.certifications_statistics.CertificationsStatisticsTransformationService;
import de.adesso.fischereiregister.registerservice.statistics.licenses_statistics.LicensesStatistics;
import de.adesso.fischereiregister.registerservice.statistics.licenses_statistics.LicensesStatisticsTransformationService;
import de.adesso.fischereiregister.registerservice.statistics.taxes_statistics.TaxesStatistics;
import de.adesso.fischereiregister.registerservice.statistics.taxes_statistics.TaxesStatisticsTransformationService;
import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;
import de.adesso.fischereiregister.view.bans_statistics.services.BansStatisticsViewService;
import de.adesso.fischereiregister.view.certifications_statistics.persistence.CertificationsStatisticsView;
import de.adesso.fischereiregister.view.certifications_statistics.services.CertificationsStatisticsViewService;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.model.FederalStateAbbreviation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping
@Slf4j
@AllArgsConstructor
public class StatisticsController implements api.StatisticsApi {

    private final LicensesStatisticsViewService licensesStatisticsViewService;
    private final LicensesStatisticsTransformationService licensesStatisticsTransformationService;

    private final TaxesStatisticsViewService taxesStatisticsViewService;
    private final TaxesStatisticsTransformationService taxesStatisticsTransformationService;

    private final BansStatisticsViewService bansStatisticsViewService;
    private final BansStatisticsTransformationService bansStatisticsTransformationService;

    private final CertificationsStatisticsViewService certificationsStatisticsViewService;
    private final CertificationsStatisticsTransformationService certificationsStatisticsTransformationService;
    private final InspectorProtocolService inspectorProtocolService;

    @Override
    public ResponseEntity<List<org.openapitools.model.LicensesStatistics>> statisticsControllerGetRegularLicensesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        return ResponseEntity.ok(getLicensesStatisticsForLicenseType(LicenseType.REGULAR, year, office, federalState));
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.LicensesStatistics>> statisticsControllerGetLimitedLicensesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        return ResponseEntity.ok(getLicensesStatisticsForLicenseType(LicenseType.LIMITED, year, office, federalState));
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.LicensesStatistics>> statisticsControllerGetVacationLicensesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        return ResponseEntity.ok(getLicensesStatisticsForLicenseType(LicenseType.VACATION, year, office, federalState));
    }

    private List<org.openapitools.model.LicensesStatistics> getLicensesStatisticsForLicenseType(LicenseType licenseType, List<Integer> years, String office, FederalStateAbbreviation federalState) {
        List<LicensesStatisticsView> licensesStatisticsViews;

        try {
            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = years != null && !years.isEmpty() ? years : licensesStatisticsViewService.getAvailableYears();

            if (office != null && !office.isEmpty()) {
                // Office has priority over federalState
                log.info("Fetching licensesStatisticsViews for licenseType: {} with office: {} and years: {}", licenseType, office, yearsToQuery);
                licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndOfficeAndYears(licenseType, office, yearsToQuery);
            } else if (federalState != null) {
                // If no office specified but federalState is provided
                String federalStateValue = federalState.getValue();
                log.info("Fetching licensesStatisticsViews for licenseType: {} with federalState: {} and years: {}", licenseType, federalStateValue, yearsToQuery);
                licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndFederalStateAndYears(licenseType, federalStateValue, yearsToQuery);
            } else {
                // If neither office nor federalState specified
                log.info("Fetching licensesStatisticsViews for licenseType: {} for all regions and years: {}", licenseType, yearsToQuery);
                licensesStatisticsViews = licensesStatisticsViewService.getStatisticsByLicenseTypeAndYears(licenseType, yearsToQuery);
            }

            // Log the number of statistics views fetched
            log.info("Fetched {} LicensesStatisticsViews for licenseType: {}", licensesStatisticsViews.size(), licenseType);

            final List<LicensesStatistics> licensesStatistics = licensesStatisticsTransformationService.transformToLicensesStatistics(licensesStatisticsViews, yearsToQuery);

            return LicensesStatisticsMapper.INSTANCE.licensesStatisticsListToApiLicensesStatisticsList(licensesStatistics);

        } catch (Exception e) {
            log.error("Error fetching licensesStatisticsViews for licenseType {}: {}", licenseType, e.getMessage(), e);
            throw new RuntimeException("Failed to fetch licensesStatisticsViews for licenseType " + licenseType, e);
        }
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.TaxesStatistics>> statisticsControllerGetTaxesStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        List<TaxesStatisticsView> taxesStatisticsViews;

        try {
            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = year != null && !year.isEmpty() ? year : taxesStatisticsViewService.getAvailableYears();

            if (office != null && !office.isEmpty()) {
                // Office has priority over federalState
                log.info("Fetching taxesStatisticsViews with office: {} and years: {}", office, yearsToQuery);
                taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByOfficeAndYears(office, yearsToQuery);
            } else if (federalState != null) {
                // If no office specified but federalState is provided
                String federalStateValue = federalState.getValue();
                log.info("Fetching taxesStatisticsViews with federalState: {} and years: {}", federalStateValue, yearsToQuery);
                taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByFederalStateAndYears(federalStateValue, yearsToQuery);
            } else {
                // If neither office nor federalState specified
                log.info("Fetching taxesStatisticsViews for all regions and years: {}", yearsToQuery);
                taxesStatisticsViews = taxesStatisticsViewService.getStatisticsByYears(yearsToQuery);
            }

            // Log the number of statistics views fetched
            log.info("Fetched {} TaxesStatisticsViews", taxesStatisticsViews.size());

            final List<TaxesStatistics> taxesStatistics = taxesStatisticsTransformationService.transformToTaxesStatistics(taxesStatisticsViews, yearsToQuery);

            return ResponseEntity.ok(TaxesStatisticsMapper.INSTANCE.taxesStatisticsListToApiTaxesStatisticsList(taxesStatistics));

        } catch (Exception e) {
            log.error("Error fetching taxesStatisticsViews: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch taxesStatisticsViews", e);
        }
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.BansStatistics>> statisticsControllerGetBansStatistics(List<Integer> year, FederalStateAbbreviation federalState) {
        List<BansStatisticsView> bansStatisticsViews;

        try {
            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = year != null && !year.isEmpty() ? year : bansStatisticsViewService.getAvailableYears();

            if (federalState != null) {
                // If federalState is provided
                String federalStateValue = federalState.getValue();
                log.info("Fetching bansStatisticsViews with federalState: {} and years: {}", federalStateValue, yearsToQuery);
                bansStatisticsViews = bansStatisticsViewService.getStatisticsByFederalStateAndYears(federalStateValue, yearsToQuery);
            } else {
                // If no federalState specified
                log.info("Fetching bansStatisticsViews for all regions and years: {}", yearsToQuery);
                bansStatisticsViews = bansStatisticsViewService.getStatisticsByYears(yearsToQuery);
            }

            // Log the number of statistics views fetched
            log.info("Fetched {} BansStatisticsViews", bansStatisticsViews.size());

            final List<BansStatistics> bansStatistics = bansStatisticsTransformationService.transformToBansStatistics(bansStatisticsViews, yearsToQuery);

            return ResponseEntity.ok(BansStatisticsMapper.INSTANCE.bansStatisticsListToApiBansStatisticsList(bansStatistics));

        } catch (Exception e) {
            log.error("Error fetching bansStatisticsViews: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch bansStatisticsViews", e);
        }
    }

    @Override
    public ResponseEntity<List<org.openapitools.model.CertificationsStatistics>> statisticsControllerGetCertificationsStatistics(List<Integer> year, String office, FederalStateAbbreviation federalState) {
        List<CertificationsStatisticsView> certificationsStatisticsViews;

        try {
            // If years list is empty, get all available years
            final List<Integer> yearsToQuery = year != null && !year.isEmpty() ? year : certificationsStatisticsViewService.getAvailableYears();

            if (office != null && !office.isEmpty()) {
                // Office has priority over federalState
                log.info("Fetching certificationsStatisticsViews with issuer: {} and years: {}", office, yearsToQuery);
                certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByIssuerAndYears(office, yearsToQuery);
            } else if (federalState != null) {
                // If no office specified but federalState is provided
                String federalStateValue = federalState.getValue();
                log.info("Fetching certificationsStatisticsViews with federalState: {} and years: {}", federalStateValue, yearsToQuery);
                certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByFederalStateAndYears(federalStateValue, yearsToQuery);
            } else {
                // If neither office nor federalState specified
                log.info("Fetching certificationsStatisticsViews for all regions and years: {}", yearsToQuery);
                certificationsStatisticsViews = certificationsStatisticsViewService.getStatisticsByYears(yearsToQuery);
            }

            // Log the number of statistics views fetched
            log.info("Fetched {} CertificationsStatisticsViews", certificationsStatisticsViews.size());

            final List<CertificationsStatistics> certificationsStatistics = certificationsStatisticsTransformationService.transformToCertificationsStatistics(certificationsStatisticsViews, yearsToQuery);

            return ResponseEntity.ok(CertificationsStatisticsMapper.INSTANCE.certificationsStatisticsListToApiCertificationsStatisticsList(certificationsStatistics));

        } catch (Exception e) {
            log.error("Error fetching certificationsStatisticsViews: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch certificationsStatisticsViews", e);
        }
    }

    @Override
    public ResponseEntity<?> statisticsControllerGetInspectionsStatistics(@Valid List<Integer> year, @Valid FederalStateAbbreviation federalState) {
        List<InspectionsStatisticsResult> results = inspectorProtocolService.getInspectionsStatistics(year, federalState != null ? federalState.getValue() : null);

        return ResponseEntity.ok(results);
    }
}
