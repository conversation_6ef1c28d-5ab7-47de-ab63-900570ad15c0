package de.adesso.fischereiregister.registerservice.security;


import de.adesso.fischereiregister.core.model.user.UserRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.session.RegisterSessionAuthenticationStrategy;
import org.springframework.security.web.authentication.session.SessionAuthenticationStrategy;
import org.springframework.security.web.session.HttpSessionEventPublisher;

@Configuration
@EnableWebSecurity
@Slf4j
public class SecurityConfiguration {

    private static final String ROLE_INSPECTOR = UserRole.INSPECTOR.getValue();
    private static final String ROLE_OFFICIAL = UserRole.OFFICIAL.getValue();
    private static final String ROLE_EXAM_DATA_CREATOR = UserRole.EXAM_DATA_CREATOR.getValue();
    private static final String ROLE_LIMITED_LICENSE_CREATOR = UserRole.LIMITED_LICENSE_CREATOR.getValue();
    private static final String ROLE_ADMIN = UserRole.ADMIN.getValue();
    private static final String ROLE_ONLINE_SERVICE = UserRole.ONLINE_SERVICE.getValue();
    private static final String ROLE_BAN_MANAGER = UserRole.BAN_MANAGER.getValue();
    private static final String ROLE_CARD_PRINTER = UserRole.CARD_PRINTER.getValue();
    private static final String ROLE_EXAM_SOFTWARE = UserRole.EXAM_SOFTWARE.getValue();

    SecurityConfiguration() {
    }

    @Bean
    public SecurityProperties securityProperties() {
        return new SecurityProperties();
    }

    @Bean
    public String federalStateIdentifier() {
        return "federalState";
    }

    @Bean
    public String idClaimIdentifier() {
        return "sub";
    }

    @Bean
    public String examinationClaimIdentifier() {
        return "examination";
    }

    @Bean
    public String issuerClaimIdentifier() {
        return "issuer";
    }

    @Bean
    public String officeAddressClaimIdentifier() {
        return "officeAddress";
    }

    @Bean
    public UserDetailsService userDetailsService() {
        return new UserDetailsServiceImpl();
    }

    @Bean
    public SessionRegistry sessionRegistry() {
        return new SessionRegistryImpl();
    }

    @Bean
    protected SessionAuthenticationStrategy sessionAuthenticationStrategy() {
        return new RegisterSessionAuthenticationStrategy(sessionRegistry());
    }

    @Bean
    public HttpSessionEventPublisher httpSessionEventPublisher() {
        return new HttpSessionEventPublisher();
    }

    @Bean
    public JwtRolesConverter jwtRolesConverter() {
        return new JwtRolesConverterImpl(securityProperties());
    }

    /**
     * Defines which authorization is needed to access a route. (As an alternative
     * annotations could be used on the
     * methods of the Rest - Controller Methods, but we want to keep alls authorization configuration in a single place,
     * this is the reason we do not use the anotation on the controllers right now.
     * directly. see
     * <a href=
     * "https://docs.spring.io/spring-security/reference/servlet/authorization/method-security.html">Method
     * Security</a>.
     */
    @Bean
    SecurityFilterChain filterChain(HttpSecurity httpSecurity) throws Exception {
        SecurityProperties properties = securityProperties();

        httpSecurity.oauth2ResourceServer(
                oauth2 -> oauth2.jwt(jwt -> jwt.jwtAuthenticationConverter(jwtRolesConverter())));

        httpSecurity
                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers("/register-entries/fishing-licenses:digitize")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/fishing-licenses/vacation")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/fishing-certificates")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/taxes")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/ban")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_BAN_MANAGER)
                        .requestMatchers("/register-entries/{registerEntryId}/ban/permanent")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_BAN_MANAGER)
                        .requestMatchers("/register-entries/{registerEntryId}/ban/temporary")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_BAN_MANAGER)
                        .requestMatchers("/register-entries/{registerEntryId}/identification-documents")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/identification-documents:send-mail")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/identification-documents/{documentId}/pdf")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/os/v1/fishing-license/tax")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ONLINE_SERVICE)
                        .requestMatchers("/os/v1/fishing-license")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ONLINE_SERVICE)
                        .requestMatchers("/os/v1/fishing-license/vacation")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ONLINE_SERVICE)
                        .requestMatchers("/os/v1/fishing-license/limited")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ONLINE_SERVICE)
                        .requestMatchers("/os/v1/fishing-license:replace")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ONLINE_SERVICE)
                        .requestMatchers("/os/v1/fishing-license/vacation:extend")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ONLINE_SERVICE)
                        .requestMatchers("/register-entries/person")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/person")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/fishing-licenses:validate")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_INSPECTOR)
                        .requestMatchers("/register-entries/{registerEntryId}")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/fishing-licenses/{licenseId}/orders")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/fishing-licenses/{licenseId}:extend")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/jurisdiction:move")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/fishing-licenses/regular")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/fishing-licenses/vacation")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/register-entries/{registerEntryId}/fishing-licenses/limited")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_LIMITED_LICENSE_CREATOR)
                        .requestMatchers("/register-entries/fishing-licenses/limited")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_LIMITED_LICENSE_CREATOR)
                        .requestMatchers("/register-entries/{registerEntryId}/limited-license-application:reject")
                        .hasAnyAuthority(properties.getPrefixRealmRole() + ROLE_LIMITED_LICENSE_CREATOR)
                        .requestMatchers("/fishing-tax/calculate")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/preliminary-register-entries")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_EXAM_DATA_CREATOR)
                        .requestMatchers("/preliminary-register-entries/certificate")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_EXAM_SOFTWARE)
                        .requestMatchers("/preliminary-register-entries/certificates/{fishingCertificateId}/pdf")
                        .hasAnyAuthority(
                                properties.getPrefixRealmRole() + ROLE_EXAM_DATA_CREATOR,
                                properties.getPrefixRealmRole() + ROLE_EXAM_SOFTWARE)
                        .requestMatchers("/tenant-configuration/load")
                        .hasAnyAuthority(
                                properties.getPrefixRealmRole() + ROLE_OFFICIAL,
                                properties.getPrefixRealmRole() + ROLE_EXAM_DATA_CREATOR
                        )
                        .requestMatchers("/tenant/license-information")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/tenant/tax-information")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/namings")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/licenses/regular")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/licenses/vacation")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/licenses/limited")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/taxes")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/bans")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/certifications")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/metadata/offices")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/metadata/certification-issuers")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/statistics/inspections")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_OFFICIAL)
                        .requestMatchers("/storage/**")
                        .authenticated()
                        .requestMatchers("/nationalities")
                        .authenticated()
                        .requestMatchers("/orders/{orderId}/status")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_CARD_PRINTER)
                        .requestMatchers("/orders/{orderId}")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_CARD_PRINTER)
                        .requestMatchers("/admin/truncate-all")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ADMIN)
                        .requestMatchers("/import-test-data")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ADMIN)
                        .requestMatchers("/admin/event-stream/{registerEntryId}")
                        .hasAuthority(properties.getPrefixRealmRole() + ROLE_ADMIN)
                        .requestMatchers("/actuator/health/*")
                        .permitAll()
                );

        return httpSecurity.build();
    }

}
