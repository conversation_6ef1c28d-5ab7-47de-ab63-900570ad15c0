package de.adesso.fischereiregister.registerservice.statistics;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.protocol.service.InspectorProtocolService;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

import static org.hamcrest.Matchers.greaterThanOrEqualTo;
import static org.hamcrest.Matchers.hasSize;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = RegisterServiceApplication.class)
@AutoConfigureMockMvc(addFilters = false)
@WithMockSecurityContext
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class InspectionsStatisticsReadIntegrationTest {

    // greaterThanOrEqualTo is used for the assertions as the data is still contaminated by the file test-data.xml, please correct when the file gets deleted

    public static final String PARAM_YEAR = "year";
    public static final String PARAM_FEDERAL_STATE = "federalState";
    private static final String TEST_FEDERAL_STATE_SH = "SH";
    private static final String TEST_FEDERAL_STATE_HH = "HH";
    private static final int CURRENT_YEAR = LocalDate.now().getYear();
    private static final int PREVIOUS_YEAR = CURRENT_YEAR - 1;

    @Autowired
    private InspectorProtocolService inspectorProtocolService;

    @Autowired
    private MockMvc mvc;

    @BeforeAll
    void setUp() {
        // Create inspection protocol entries for testing
        LocalDateTime currentYearDateTime = LocalDateTime.of(CURRENT_YEAR, 6, 15, 10, 30, 0);
        LocalDateTime previousYearDateTime = LocalDateTime.of(PREVIOUS_YEAR, 8, 20, 14, 45, 0);

        // Create inspection entries for current year - SH
        inspectorProtocolService.createProtocolEntry(
                "inspector1-" + UUID.randomUUID(),
                "register-entry-1",
                TEST_FEDERAL_STATE_SH,
                currentYearDateTime
        );

        inspectorProtocolService.createProtocolEntry(
                "inspector2-" + UUID.randomUUID(),
                "register-entry-2",
                TEST_FEDERAL_STATE_SH,
                currentYearDateTime.plusHours(2)
        );

        // Create inspection entries for current year - HH
        inspectorProtocolService.createProtocolEntry(
                "inspector3-" + UUID.randomUUID(),
                "register-entry-3",
                TEST_FEDERAL_STATE_HH,
                currentYearDateTime.plusDays(1)
        );

        // Create inspection entries for previous year - SH
        inspectorProtocolService.createProtocolEntry(
                "inspector1-" + UUID.randomUUID(),
                "register-entry-4",
                TEST_FEDERAL_STATE_SH,
                previousYearDateTime
        );

        // Create additional inspection by same inspector (should not increase active inspectors count)
        inspectorProtocolService.createProtocolEntry(
                "inspector1-" + UUID.randomUUID(),
                "register-entry-5",
                TEST_FEDERAL_STATE_SH,
                currentYearDateTime.plusMinutes(30)
        );
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections
            Verify that the inspections statistics endpoint can be reached and delivers the proper information.
            """)
    void callGetInspectionsStatisticsSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Data object exists
                .andExpect(jsonPath("$[0].data.activeInspectors").value(greaterThanOrEqualTo(1))) // At least 1 active inspector in SH
                .andExpect(jsonPath("$[0].data.numberOfInspections").value(greaterThanOrEqualTo(2))); // At least 2 inspections in SH
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections with federal state parameter
            Verify that the inspections statistics endpoint correctly filters by federal state.
            """)
    void callGetInspectionsStatisticsWithFederalStateFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_HH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Data object exists
                .andExpect(jsonPath("$[0].data.activeInspectors").value(greaterThanOrEqualTo(1))) // At least 1 active inspector in HH
                .andExpect(jsonPath("$[0].data.numberOfInspections").value(greaterThanOrEqualTo(1))); // At least 1 inspection in HH
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections with year parameter
            Verify that the inspections statistics endpoint correctly filters by year.
            """)
    void callGetInspectionsStatisticsWithYearFilter() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$[0].year").value(PREVIOUS_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Data object exists
                .andExpect(jsonPath("$[0].data.activeInspectors").value(greaterThanOrEqualTo(1))) // At least 1 active inspector in SH in previous year
                .andExpect(jsonPath("$[0].data.numberOfInspections").value(greaterThanOrEqualTo(1))); // At least 1 inspection in SH in previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections with multiple year parameters
            Verify that the inspections statistics endpoint correctly handles multiple year parameters.
            """)
    void callGetInspectionsStatisticsWithMultipleYears() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_YEAR, String.valueOf(PREVIOUS_YEAR))
                        .param(PARAM_FEDERAL_STATE, TEST_FEDERAL_STATE_SH)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                .andExpect(jsonPath("$", hasSize(2))) // Two years of data
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                .andExpect(jsonPath("$[0].data").exists()) // Data object exists for current year
                .andExpect(jsonPath("$[1].data").exists()); // Data object exists for previous year
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections without federalState parameter
            Verify that the inspections statistics endpoint returns data for all federal states when no federalState parameter is provided.
            """)
    void callGetInspectionsStatisticsWithoutFederalStateSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all federal states for the current year
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Data object exists
                .andExpect(jsonPath("$[0].data.activeInspectors").value(greaterThanOrEqualTo(2))) // At least 2 active inspectors (SH + HH)
                .andExpect(jsonPath("$[0].data.numberOfInspections").value(greaterThanOrEqualTo(3))); // At least 3 inspections (2 SH + 1 HH)
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections without parameters
            Verify that the inspections statistics endpoint returns all available data when no parameters are provided.
            """)
    void callGetInspectionsStatisticsWithoutParametersSuccessful() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isNotEmpty())
                // Data from all years and federal states
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // First result year (current year)
                .andExpect(jsonPath("$[1].year").value(PREVIOUS_YEAR)) // Second result year (previous year)
                .andExpect(jsonPath("$[*].data").exists()); // Data objects exist
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections with non-existent year parameter
            Verify that the inspections statistics endpoint returns zero-filled data for a non-existent year.
            """)
    void callGetInspectionsStatisticsWithNonExistentYear() throws Exception {
        String nonExistentYear = String.valueOf(2000); // Use a year with no data

        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .param(PARAM_YEAR, nonExistentYear)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()) // Response is an array
                .andExpect(jsonPath("$").isNotEmpty()) // Non-empty array with zero-filled data
                .andExpect(jsonPath("$[0].year").value(Integer.parseInt(nonExistentYear))) // Year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Data object exists
                .andExpect(jsonPath("$[0].data.activeInspectors").value(0)) // Zero active inspectors
                .andExpect(jsonPath("$[0].data.numberOfInspections").value(0)); // Zero inspections
    }

    @Test
    @DisplayName("""
            GET /api/statistics/inspections with non-existent federal state parameter
            Verify that the inspections statistics endpoint returns zero-filled data for a non-existent federal state.
            """)
    void callGetInspectionsStatisticsWithNonExistentFederalState() throws Exception {
        mvc.perform(MockMvcRequestBuilders.get("/statistics/inspections")
                        .param(PARAM_YEAR, String.valueOf(CURRENT_YEAR))
                        .param(PARAM_FEDERAL_STATE, "XX") // Non-existent federal state
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray()) // Response is an array
                .andExpect(jsonPath("$").isNotEmpty()) // Non-empty array with zero-filled data
                .andExpect(jsonPath("$[0].year").value(CURRENT_YEAR)) // Year matches parameter
                .andExpect(jsonPath("$[0].data").exists()) // Data object exists
                .andExpect(jsonPath("$[0].data.activeInspectors").value(0)) // Zero active inspectors
                .andExpect(jsonPath("$[0].data.numberOfInspections").value(0)); // Zero inspections
    }
}
