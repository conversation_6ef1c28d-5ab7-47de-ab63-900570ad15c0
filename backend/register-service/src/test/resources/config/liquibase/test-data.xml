<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.8.xsd">


    <!-- Insert data for register_entry_search_view -->
    <changeSet id="1.0.2-4" author="test">
        <insert tableName="register_entry_search_view">
            <column name="register_id" value="12345678-1234-1234-1234-123456789012"/>
            <column name="normalized_name" value="max mustermann"/>
            <column name="normalized_birthplace" value="musterstadt"/>
            <column name="birthdate" value="01.01.2000"/>
            <column name="identification_numbers" value="SH85598631191522"/>
            <column name="data"
                    value='{"registerId":"e4b6af13-1feb-4a4d-9c46-76298a0611cf","person":{"title":"Dr.","firstname":"Max","lastname":"Mustermann","birthname":"","birthplace":"Musterstadt","birthdate":"01.01.2000"},"fishingLicenses":[{"number":"SH85-5986-3119-1522"}],"qualificationsProofs":null}'/>
        </insert>
        <insert tableName="register_entry_search_view">
            <column name="register_id" value="12345678-1234-1234-1234-123456789013"/>
            <column name="normalized_name" value="matz mustermann"/>
            <column name="normalized_birthplace" value="musterstadthausen"/>
            <column name="birthdate" value="01.01.2000"/>
            <column name="identification_numbers" value="SH85598631191523"/>
            <column name="data"
                    value='{"registerId":"e4b6af13-1feb-4a4d-9c46-76298a0611cf","person":{"title":"Dr.","firstname":"Matz","lastname":"Mustermann","birthname":"","birthplace":"Musterstadthausen","birthdate":"01.01.2000"},"fishingLicenses":[{"number":"SH85-5986-3119-1523"}],"qualificationsProofs":null}'/>
        </insert>
        <insert tableName="register_entry_search_view">
            <column name="register_id" value="039110dd-732d-46fa-9d3a-188fc2960ed9"/>
            <column name="normalized_name" value="matz mustermann"/>
            <column name="normalized_birthplace" value="musterstadthausen"/>
            <column name="birthdate" value="01.01.2000"/>
            <column name="identification_numbers" value="SH85598631191524"/>
            <column name="data"
                    value='{"registerId":"039110dd-732d-46fa-9d3a-188fc2960ed9","person":{"title":"Dr.","firstname":"Matz","lastname":"Mustermann","birthname":"","birthplace":"Musterstadthausen","birthdate":"01.01.2000"},"fishingLicenses":[{"number":"SH85-5986-3119-1524"}],"qualificationsProofs":null}'/>
        </insert>

        <insert tableName="register_entry_search_view">
            <column name="register_id" value="e772d64a-039a-4245-869c-1a707bb7d602"/>
            <column name="normalized_name" value="matz mustermann"/>
            <column name="normalized_birthplace" value="musterstadthausen"/>
            <column name="birthdate" value="01.01.2000"/>
            <column name="identification_numbers" value="ZF91177410258895"/>
            <column name="data"
                    value='{"registerId":"e772d64a-039a-4245-869c-1a707bb7d602","person":{"title":"Dr.","firstname":"Matz","lastname":"Mustermann","birthname":"","birthplace":"Musterstadthausen","birthdate":"01.01.2000"},"qualificationsProofs":null}'/>
        </insert>

    </changeSet>

    <!-- Insert data for identification_document_view -->
    <changeSet id="1.0.4-2" author="test">
        <insert tableName="identification_document_view">
            <column name="id" value="100"/>
            <column name="register_id" value="12345678-1234-1234-1234-123456789012"/>
            <column name="identification_document_id" value="2d073b7d-a961-45e3-89e6-85cd19123743"/>
            <column name="salt" value="bEo31ut8sJj"/>
            <column name="license_number" value="SH12111122223331"/>
        </insert>
        <insert tableName="identification_document_view">
            <column name="id" value="200"/>
            <column name="register_id" value="e4b6af13-1feb-4a4d-9c46-76298a0611cf"/>
            <column name="identification_document_id" value="1236af13-1feb-4a4d-9c46-76298a0611cf"/>
            <column name="salt" value="bEo31ut5sJj"/>
            <column name="license_number" value="SH12111122223123"/>
            <column name="document_type" value="PDF"/>
        </insert>
        <insert tableName="identification_document_view">
            <column name="id" value="3"/>
            <column name="register_id" value="e4b6af13-1feb-4a4d-9c46-76298a0611cf"/>
            <column name="identification_document_id" value="4566af13-1feb-4a4d-9c46-76298a0611cf"/>
            <column name="salt" value="bEo31ut5sJj"/>
            <column name="document_type" value="PDF"/>
        </insert>
        <insert tableName="identification_document_view">
            <column name="id" value="4"/>
            <column name="register_id" value="3049614a-61d9-4e14-b7cb-38ac25bf4d4c"/>
            <column name="identification_document_id" value="a7f405d1-fcbf-4505-8303-47b14b5b8c80"/>
            <column name="salt" value="bEo31ut5sJj"/>
            <column name="license_number" value="SH05151912316481"/>
            <column name="document_type" value="PDF"/>
        </insert>
    </changeSet>

    <!-- Insert data for register_entry_search_view -->
    <changeSet id="1.0.4-1" author="test">
        <insert tableName="register_entry_view">
            <column name="register_id" value="12345678-1234-1234-1234-123456789012"/>
            <column name="data"
                    value='{"registerId":"12345678-1234-1234-1234-123456789012","person":{"title":"Dr.","firstname":"Max","lastname":"Mustermann","birthname":"","birthplace":"","birthdate":"01.01.2000","address":{"street":"Skomakargatan","streetNumber":"9","postcode":"111 29","city":"Stockholm"},"nationality":"","email":null},"jurisdiction":{"federalState":"null"},"ban":{"fileNumber":null,"reportedBy":null,"from":null,"to":null, "at": "2025-01-01"},"fishingLicenses":[{"number":"SH85598631191522","legacyNumber":null, "issuingFederalState": "SH", "type":"VACATION","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}],"fees":[],"taxes":[],"qualificationsProofs":null,"identificationDocuments":[{"issuedDate":[2024,8,8],"documentId":"2d073b7d-a961-45e3-89e6-85cd19123743","type":"PDF","fishingLicense":{"number":"SH12111122223331", "issuingFederalState": "SH", "legacyNumber":null,"type":"VACATION","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"consentInfo":{"gdprAccepted":true,"selfDisclosureAccepted":true,"submittedByThirdParty":null}}'/>
        </insert>
        <insert tableName="register_entry_view">
            <column name="register_id" value="e4b6af13-1feb-4a4d-9c46-76298a0611cf"/>
            <column name="data"
                    value='{"registerId":"e4b6af13-1feb-4a4d-9c46-76298a0611cf","person":{"title":"Dr.","firstname":"Max","lastname":"Mustermann","birthname":"","birthplace":"","birthdate":"01.01.2000","address":{"street":"Skomakargatan","streetNumber":"9","postcode":"111 29","city":"Stockholm"},"nationality":"","email":null},"jurisdiction":{"federalState":"null"},"ban":{"banId":"0a5c722d-0263-4aa0-92cd-da1f7a7f964b","fileNumber":null,"reportedBy":null,"from":null,"to":null, "at": "2025-01-01"},"fishingLicenses":[{"number":"SH85598631191522", "issuingFederalState": "SH","legacyNumber":null,"type":"VACATION","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}],"fees":[],"taxes":[],"qualificationsProofs":null,"identificationDocuments":[{"issuedDate":[2024,8,8],"documentId":"2d073b7d-a961-45e3-89e6-85cd19123743","type":"PDF","fishingLicense":{"number":"SH12111122223331", "issuingFederalState": "SH","legacyNumber":null,"type":"VACATION","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"consentInfo":{"gdprAccepted":true,"selfDisclosureAccepted":true,"submittedByThirdParty":null}}'/>
        </insert>
        <insert tableName="register_entry_view">
            <column name="register_id" value="3049614a-61d9-4e14-b7cb-38ac25bf4d4c"/>
            <column name="data"
                    value='{"registerId":"3049614a-61d9-4e14-b7cb-38ac25bf4d4c","person":{"title":"Dr.","firstname":"Max","lastname":"lastname","birthname":"birthname","birthplace":"city","birthdate":"08.08.2024"},"jurisdiction":{"federalState":"SH"},"fishingLicenses":[{"number":"SH05151912316481", "issuingFederalState": "SH","legacyNumber":"11112","issuingFederalState":"SH","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}],"fees":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"taxes":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"qualificationsProofs":[{"fishingCertificateId":"333","federalState":"NW","examinerId":null,"passedOn":[2023,8,8],"issuedBy":"Fischschule Fischbude GmbH"}],"identificationDocuments":[{"issuedDate":[2024,9,17],"documentId":"a7f405d1-fcbf-4505-8303-47b14b5b8c80","type":"PDF","fishingLicense":null,"tax":{ "taxId": "1112233", "federalState": "sh", "validFrom": [2024,1,1], "validTo": [2025,1,1] }},{"issuedDate":[2024,9,17],"documentId":"54cb8382-dac7-4bb9-8684-57172619eb18","type":"PDF","fishingLicense":{"number":"SH05151912316481", "issuingFederalState": "SH","legacyNumber":"11112","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"consentInfo":{"gdprAccepted":true,"selfDisclosureAccepted":true,"submittedByThirdParty":null}}'/>
        </insert>
        <insert tableName="register_entry_view">
            <column name="register_id" value="351031b4-d8ca-4380-a49f-47159ac68b8b"/>
            <column name="data"
                    value='{"registerId":"351031b4-d8ca-4380-a49f-47159ac68b8b","person":{"title":"Dr.","firstname":"Max","lastname":"lastname","birthname":"birthname","birthplace":"city","birthdate":"08.08.2024"},"fishingLicenses":[{"number":"SH85598631191522", "issuingFederalState": "SH","legacyNumber":"11112","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}],"fees":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"taxes":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"qualificationsProofs":[{"fishingCertificateId":"333","federalState":"NW","examinerId":null,"passedOn":[2023,8,8],"issuedBy":"Fischschule Fischbude GmbH"}],"identificationDocuments":[{"issuedDate":[2024,9,17],"documentId":"a7f405d1-fcbf-4505-8303-47b14b5b8c80","type":"PDF","fishingLicense":{"number":"SH05151912316481", "issuingFederalState": "SH","legacyNumber":"11112","type":"REGULAR","validityPeriods":[]}, "tax": null},{"issuedDate":[2024,9,17],"documentId":"54cb8382-dac7-4bb9-8684-57172619eb18","type":"PDF","fishingLicense":{"number":"SH05151912316481", "issuingFederalState": "SH","legacyNumber":"11112","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"consentInfo":{"gdprAccepted":true,"selfDisclosureAccepted":true,"submittedByThirdParty":null}}'/>
        </insert>
    </changeSet>
    <changeSet id="1.0.6" author="madalina-iuliana.gheorghe">
        <insert tableName="domain_event_entry">
            <column name="global_index" value="2000"/>
            <column name="event_identifier" value="0373e03a-49ff-4b69-875a-a347e27ff20a"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerId":"e4b6af13-1feb-4a4d-9c46-76298a0611cf","salt":"eyzwxLXXfu","person":{"title":"Dr.","firstname":"Max","lastname":"lastname","birthname":"birthname","birthplace":"city","birthdate":"08.08.2024","address":{"office":null,"deliverTo":null,"street":"street","streetNumber":"12","postcode":"12334","city":"Kiel","detail":null},"officeAddress":null,"nationality":"deutsch","email":null},"jurisdiction":{"federalState":"SH"},"fishingLicense":{"number":"SH05151912316480", "issuingFederalState": "SH","legacyNumber":"11112","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]},"fees":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"taxes":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"qualificationsProofs":[{"fishingCertificateId":"333","federalState":"NW","examinerId":null,"passedOn":[2023,8,8],"issuedBy":"Fischschule Fischbude GmbH"}],"identificationDocuments":[{"issuedDate":[2024,9,17],"documentId":"bda83b17-1929-42fa-9379-f1dcba14fcd5","type":"PDF","fishingLicense":null},{"issuedDate":[2024,9,17],"documentId":"401be53c-a1d4-4600-bdc0-fe0d9772539b","type":"PDF","fishingLicense":{"number":"SH05151912316480", "issuingFederalState": "SH","legacyNumber":"11112","issuingFederalState":"SH","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"consentInfo":{"gdprAccepted":true,"selfDisclosureAccepted":true,"submittedByThirdParty":null},"issuedByOffice":"testOffice"}}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent"/>
            <column name="time_stamp" value="2024-08-28T12:13:58.262Z"/>
            <column name="aggregate_identifier" value="e4b6af13-1feb-4a4d-9c46-76298a0611cf"/>
            <column name="sequence_number" value="0"/>
            <column name="type" value="RegisterEntry"/>
        </insert>
        <insert tableName="domain_event_entry">
            <column name="global_index" value="3000"/>
            <column name="event_identifier" value="b89dde8d-3b17-46e0-826a-e933d63fbfaa"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerId":"1917c468-35eb-423e-b758-25990506b4f9","salt":"eyzwxLXXfu","person":{"title":"Dr.","firstname":"Max Zwei","lastname":"lastname","birthname":"birthname","birthplace":"city","birthdate":"08.08.2010","address":{"office":null,"deliverTo":null,"street":"street","streetNumber":"12","postcode":"12334","city":"Kiel","detail":null},"officeAddress":null,"nationality":"deutsch","email":null},"jurisdiction":{"federalState":"SH"},"fishingLicense":{"number":"SH05151912316481", "issuingFederalState": "SH","legacyNumber":"11113","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]},"fees":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"taxes":[{"federalState":"SH","validFrom":[2024,8,8],"validTo":[2025,8,8],"paymentInfo":{"amount":20.0,"type":"CASH"}}],"qualificationsProofs":[{"fishingCertificateId":"3334","federalState":"NW","examinerId":null,"passedOn":[2023,8,8],"issuedBy":"Fischschule Fischbude GmbH"}],"identificationDocuments":[{"issuedDate":[2024,9,17],"documentId":"00dcd035-47d8-41de-85b8-08090e371f1c","type":"PDF","fishingLicense":null},{"issuedDate":[2024,9,17],"documentId":"fbe36787-9f76-48e8-a012-344871261f27","type":"PDF","fishingLicense":{"number":"SH05151912316481", "issuingFederalState": "SH","legacyNumber":"11112","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"consentInfo":{"gdprAccepted":true,"selfDisclosureAccepted":true,"submittedByThirdParty":null},"issuedByOffice":"testOffice"}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent"/>
            <column name="time_stamp" value="2024-08-28T12:13:58.262Z"/>
            <column name="aggregate_identifier" value="1917c468-35eb-423e-b758-25990506b4f9"/>
            <column name="sequence_number" value="0"/>
            <column name="type" value="RegisterEntry"/>
        </insert>
        <insert tableName="domain_event_entry">
            <column name="global_index" value="5303"/>
            <column name="event_identifier" value="e6b66f35-2a93-42b5-b979-ada80e354b2a"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerEntryId":"afe495b5-b82f-494f-b972-cc5dbf76c9c2","qualificationsProof":{"type":"CERTIFICATE","fishingCertificateId":"ZF91177410258894","otherFormOfProofId":null,"federalState":"SH","examinerId":"d0777342-5019-4df0-ac2a-450771d6bb51","passedOn":[1990,8,8],"issuedBy":"Schleswiger Angel- und Wassersportverein"},"person":{"title":"Dr.","firstname":"Marc ID","lastname":"lastname","birthname":"birthname","birthplace":"Berlin","birthdate":"08.08.1990","address":{"office":null,"deliverTo":null,"street":"street","streetNumber":"12","postcode":"12334","city":"Kiel","detail":null},"officeAddress":null,"nationality":"deutsch","email":null}}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent"/>
            <column name="time_stamp" value="2024-10-14T12:31:41.872Z"/>
            <column name="aggregate_identifier" value="afe495b5-b82f-494f-b972-cc5dbf76c9c2"/>
            <column name="sequence_number" value="0"/>
            <column name="type" value="RegisterEntry"/>
        </insert>

        <insert tableName="domain_event_entry">
            <column name="global_index" value="5353"/>
            <column name="event_identifier" value="e6b66f35-2a93-43b5-b979-ada80e354b2a"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerId":"afe495b5-b82f-494f-b972-cc5dbf76c9c2","previousJurisdiction":{"federalState":"HH"},"newJurisdiction":{"federalState":"SH"},"consentInfo":{"gdprAccepted":null,"submittedByThirdParty":null,"selfDisclosureAccepted":null,"proofOfMoveVerified":true},"taxes":[{"taxId":"60f6ee8b-a609-4846-90b6-5416be9c88a8","federalState":"BE","validFrom":"2024-08-08","validTo":"2025-08-08","paymentInfo":{"amount":20.0,"type":"CASH"}}],"salt":"7Qopjz9NPe","issuedByOffice":null,"identificationDocuments":[{"issuedDate":"2025-02-14","documentId":"47cbfa6e-fabf-4b0a-bc41-a5260ad5c92c","type":"PDF","fishingLicense":null,"tax":{"taxId":"60f6ee8b-a609-4846-90b6-5416be9c88a8","federalState":"BE","validFrom":"2024-08-08","validTo":"2025-08-08","paymentInfo":{"amount":20.0,"type":"CASH"}},"validFrom":"2024-08-08","validTo":"2025-08-08"}],"submissionType":"ONLINE","ban":null}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.JurisdictionMovedEvent"/>
            <column name="time_stamp" value="2024-10-14T12:31:41.872Z"/>
            <column name="aggregate_identifier" value="afe495b5-b82f-494f-b972-cc5dbf76c9c2"/>
            <column name="sequence_number" value="1"/>
            <column name="type" value="RegisterEntry"/>
        </insert>

        <insert tableName="domain_event_entry">
            <column name="global_index" value="532403"/>
            <column name="event_identifier" value="12b66f35-2a93-42b5-b979-ada80e354b2a"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerEntryId":"e772d64a-039a-4245-869c-1a707bb7d602","qualificationsProof":{"type":"CERTIFICATE","fishingCertificateId":"ZF91177410258895","otherFormOfProofId":null,"federalState":"SH","examinerId":"d0777342-5019-4df0-ac2a-450771d6bb51","passedOn":[1990,8,8],"issuedBy":"Schleswiger Angel- und Wassersportverein"},"person":{"title":"Dr.","firstname":"Marc ID","lastname":"lastname","birthname":"birthname","birthplace":"Berlin","birthdate":"08.08.1990","address":{"office":null,"deliverTo":null,"street":"street","streetNumber":"12","postcode":"12334","city":"Kiel","detail":null},"officeAddress":null,"nationality":"deutsch","email":null}}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent"/>
            <column name="time_stamp" value="2024-10-14T12:31:41.872Z"/>
            <column name="aggregate_identifier" value="e772d64a-039a-4245-869c-1a707bb7d602"/>
            <column name="sequence_number" value="0"/>
            <column name="type" value="RegisterEntry"/>
        </insert>


        <insert tableName="domain_event_entry">
            <column name="global_index" value="9003"/>
            <column name="event_identifier" value="f7a73967-edc3-4721-8ce5-9d5c3854c720"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerId":"351031b4-d8ca-4380-a49f-47159ac68b8b","fishingLicense":{"number":"SH85598631191522", "issuingFederalState": "SH", "type": "REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]},"qualificationsProofs":[{"type":"CERTIFICATE","fishingCertificateId":"ZF91177410258894","otherFormOfProofId":null,"federalState":"SH","examinerId":"d0777342-5019-4df0-ac2a-450771d6bb51","passedOn":[1990,8,8],"issuedBy":"Schleswiger Angel- und Wassersportverein"}],"person":{"title":"Dr.","firstname":"Marc ID","lastname":"lastname","birthname":"birthname","birthplace":"Berlin","birthdate":"08.08.1990","address":{"office":null,"deliverTo":null,"street":"street","streetNumber":"12","postcode":"12334","city":"Kiel","detail":null},"officeAddress":null,"nationality":"deutsch","email":null},"jurisdiction":{"federalState":"SH"},"fees":[],"taxes":[], "identificationDocuments":[{"issuedDate":[2024,9,17],"documentId":"bda83b17-1929-42fa-9379-f1dcba14fcd5","type":"PDF","fishingLicense":null},{"issuedDate":[2024,9,17],"documentId":"401be53c-a1d4-4600-bdc0-fe0d9772539b","type":"PDF","fishingLicense":{"number":"SH05151912316480", "issuingFederalState": "SH","legacyNumber":"11112","issuingFederalState":"SH","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"issuedByOffice":"testOffice"}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent"/>
            <column name="time_stamp" value="2024-10-14T12:31:41.872Z"/>
            <column name="aggregate_identifier" value="351031b4-d8ca-4380-a49f-47159ac68b8b"/>
            <column name="sequence_number" value="0"/>
            <column name="type" value="RegisterEntry"/>
        </insert>
        <insert tableName="domain_event_entry">
            <column name="global_index" value="22303"/>
            <column name="event_identifier" value="8582feb4-d5a4-4aef-8179-402a4d3a34c1"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerId":"8582feb4-d5a4-4aef-8179-402a4d3aefc1","fishingLicense":{"number":"SH67781711843692", "issuingFederalState": "SH", "type": "REGULAR"},"qualificationsProofs":[{"type":"CERTIFICATE","fishingCertificateId":"ZF91177410258894","otherFormOfProofId":null,"federalState":"SH","examinerId":"d0777342-5019-4df0-ac2a-450771d6bb51","passedOn":[1990,8,8],"issuedBy":"Schleswiger Angel- und Wassersportverein"}],"person":{"title":"Dr.","firstname":"Marc ID","lastname":"lastname","birthname":"birthname","birthplace":"Berlin","birthdate":"08.08.1990","address":{"office":null,"deliverTo":null,"street":"street","streetNumber":"12","postcode":"12334","city":"Kiel","detail":null},"officeAddress":null,"nationality":"deutsch","email":null},"jurisdiction":{"federalState":"SH"},"fees":[],"taxes":[], "identificationDocuments":[{"issuedDate":[2024,9,17],"documentId":"bda83b17-1929-42fa-9379-f1dcba14fcd5","type":"PDF","fishingLicense":null},{"issuedDate":[2024,9,17],"documentId":"401be53c-a1d4-4600-bdc0-fe0d9772539b","type":"PDF","fishingLicense":{"number":"SH05151912316480", "issuingFederalState": "SH","legacyNumber":"11112","issuingFederalState":"SH","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"issuedByOffice":"testOffice"}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent"/>
            <column name="time_stamp" value="2024-10-14T12:31:41.872Z"/>
            <column name="aggregate_identifier" value="8582feb4-d5a4-4aef-8179-402a4d3aefc1"/>
            <column name="sequence_number" value="0"/>
            <column name="type" value="RegisterEntry"/>
        </insert>
        <insert tableName="domain_event_entry">
            <column name="global_index" value="23003"/>
            <column name="event_identifier" value="039220dd-732d-46fa-9d3a-188fc2960ed9"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerId":"039110dd-732d-46fa-9d3a-188fc2960ed9","fishingLicense":{"number":"SH85598631191524", "type": "REGULAR", "issuingFederalState": "SH","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]},"qualificationsProofs":[{"type":"CERTIFICATE","fishingCertificateId":"ZF91177410258894","otherFormOfProofId":null,"federalState":"SH","examinerId":"d0777342-5019-4df0-ac2a-450771d6bb51","passedOn":[1990,8,8],"issuedBy":"Schleswiger Angel- und Wassersportverein"}],"person":{"title":"Dr.","firstname":"Marc ID","lastname":"lastname","birthname":"birthname","birthplace":"Berlin","birthdate":"08.08.1990","address":{"office":null,"deliverTo":null,"street":"street","streetNumber":"12","postcode":"12334","city":"Kiel","detail":null},"officeAddress":null,"nationality":"deutsch","email":null},"jurisdiction":{"federalState":"SH"},"fees":[],"taxes":[], "identificationDocuments":[{"issuedDate":[2024,9,17],"documentId":"bda83b17-1929-42fa-9379-f1dcba14fcd5","type":"PDF","fishingLicense":null},{"issuedDate":[2024,9,17],"documentId":"401be53c-a1d4-4600-bdc0-fe0d9772539b","type":"PDF","fishingLicense":{"number":"SH05151912316480", "issuingFederalState": "SH","legacyNumber":"11112","issuingFederalState":"SH","type":"REGULAR","validityPeriods":[{"validFrom":[2024,8,8],"validTo":[2025,8,8]}]}}],"issuedByOffice":"testOffice"}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent"/>
            <column name="time_stamp" value="2024-10-14T12:31:41.872Z"/>
            <column name="aggregate_identifier" value="039110dd-732d-46fa-9d3a-188fc2960ed9"/>
            <column name="sequence_number" value="2"/>
            <column name="type" value="RegisterEntry"/>
        </insert>
        <insert tableName="domain_event_entry">
            <column name="global_index" value="5101"/>
            <column name="event_identifier" value="abb0594b-ba73-4fc4-adc3-7492c176c7b6"/>
            <column name="meta_data"
                    value='{"traceId":"000f38cc-1c35-41ec-b789-d136045d5ec9","correlationId":"000f38cc-1c35-41ec-b789-d136045d5ec9","userId":"a3dad33b-5a6f-4f4b-8640-a085e3793d4c","commandTimestamp":"2025-02-27T14:29:10.368889654Z"}'/>
            <column name="payload"
                    value='{"registerId":"e4b6af13-1feb-4a4d-9c46-76298a0611cf","ban":{"banId":"0a5c722d-0263-4aa0-92cd-da1f7a7f964b","fileNumber":"TEST_FILE_0","reportedBy":"SYSTEM","from":"2024-07-09","to":"2026-07-08", "at": "2024-07-09"},"jurisdiction":{"federalState":"SH"}}'/>
            <column name="payload_revision" value="null">
                <constraints nullable="true"/>
            </column>
            <column name="payload_type"
                    value="de.adesso.fischereiregister.core.events.BannedEvent"/>
            <column name="time_stamp" value="2024-10-28T12:13:58.262Z"/>
            <column name="aggregate_identifier" value="e4b6af13-1feb-4a4d-9c46-76298a0611cf"/>
            <column name="sequence_number" value="1"/>
            <column name="type" value="RegisterEntry"/>
        </insert>
    </changeSet>

</databaseChangeLog>